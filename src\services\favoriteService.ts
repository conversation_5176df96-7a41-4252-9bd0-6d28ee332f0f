import {
  collection,
  doc,
  addDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  updateDoc,
  increment
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Favorite } from '@/types';
import { COLLECTIONS, serializeDoc } from '@/firebase/firestore';

export const favoriteService = {
  async add(userId: string, linkId: string): Promise<string> {
    console.log('❤️ FavoriteService.add called:', { userId, linkId });
    
    try {
      // Check if already favorited
      const existingFavorite = await this.isFavorited(userId, linkId);
      if (existingFavorite) {
        console.log('⚠️ Link already favorited');
        throw new Error('Link already favorited');
      }

      const docRef = await addDoc(collection(db, COLLECTIONS.FAVORITES), {
        userId,
        linkId,
        createdAt: serverTimestamp()
      });

      // Update link's favorite count
      const linkRef = doc(db, COLLECTIONS.LINKS, linkId);
      await updateDoc(linkRef, {
        totalFavorites: increment(1)
      });

      console.log('✅ Favorite added successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('🚨 Error adding favorite:', error);
      throw error;
    }
  },

  async remove(userId: string, linkId: string): Promise<void> {
    console.log('❤️ FavoriteService.remove called:', { userId, linkId });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FAVORITES),
        where('userId', '==', userId),
        where('linkId', '==', linkId)
      );
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        await deleteDoc(querySnapshot.docs[0].ref);

        // Update link's favorite count
        const linkRef = doc(db, COLLECTIONS.LINKS, linkId);
        await updateDoc(linkRef, {
          totalFavorites: increment(-1)
        });

        console.log('✅ Favorite removed successfully');
      } else {
        console.log('⚠️ No favorite found to remove');
      }
    } catch (error) {
      console.error('🚨 Error removing favorite:', error);
      throw error;
    }
  },

  async isFavorited(userId: string, linkId: string): Promise<boolean> {
    console.log('❤️ FavoriteService.isFavorited called:', { userId, linkId });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FAVORITES),
        where('userId', '==', userId),
        where('linkId', '==', linkId)
      );
      const querySnapshot = await getDocs(q);
      const isFavorited = !querySnapshot.empty;
      
      console.log('❤️ Favorite status:', isFavorited);
      return isFavorited;
    } catch (error) {
      console.error('🚨 Error checking favorite status:', error);
      return false;
    }
  },

  async getByUser(userId: string): Promise<Favorite[]> {
    console.log('❤️ FavoriteService.getByUser called with userId:', userId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FAVORITES),
        where('userId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      const favorites = querySnapshot.docs.map(doc => serializeDoc<Favorite>(doc));
      
      console.log('❤️ Found', favorites.length, 'favorites for user');
      return favorites;
    } catch (error) {
      console.error('🚨 Error getting user favorites:', error);
      return [];
    }
  },

  async getByLink(linkId: string): Promise<Favorite[]> {
    console.log('❤️ FavoriteService.getByLink called with linkId:', linkId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FAVORITES),
        where('linkId', '==', linkId)
      );
      const querySnapshot = await getDocs(q);
      const favorites = querySnapshot.docs.map(doc => serializeDoc<Favorite>(doc));
      
      console.log('❤️ Found', favorites.length, 'favorites for link');
      return favorites;
    } catch (error) {
      console.error('🚨 Error getting link favorites:', error);
      return [];
    }
  }
};
