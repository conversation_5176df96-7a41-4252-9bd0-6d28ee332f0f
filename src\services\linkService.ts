import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  increment,
  QueryDocumentSnapshot,
  DocumentData
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Link, LinkWithDetails, User, Category } from '@/types';
import { COLLECTIONS, serializeDoc } from '@/firebase/firestore';

export const linkService = {
  async create(linkData: Omit<Link, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    console.log('🔗 LinkService.create called with data:', linkData);
    
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.LINKS), {
        ...linkData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalRatings: 0,
        averageRating: 0,
        totalFavorites: 0
      });

      // Update user's total links count
      if (linkData.userId) {
        const userRef = doc(db, COLLECTIONS.USERS, linkData.userId);
        await updateDoc(userRef, {
          totalLinks: increment(1)
        });
      }

      console.log('✅ Link created successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('🚨 Error creating link:', error);
      throw error;
    }
  },

  async getById(id: string): Promise<Link | null> {
    console.log('🔗 LinkService.getById called with id:', id);
    
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.LINKS, id));
      if (docSnap.exists()) {
        const linkData = serializeDoc<Link>(docSnap);
        console.log('🔗 Link data loaded:', linkData);
        return linkData;
      } else {
        console.log('🔗 No link found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in linkService.getById:', error);
      return null;
    }
  },

  async update(id: string, data: Partial<Link>): Promise<void> {
    console.log('🔗 LinkService.update called:', { id, data });
    
    try {
      await updateDoc(doc(db, COLLECTIONS.LINKS, id), {
        ...data,
        updatedAt: serverTimestamp()
      });
      console.log('✅ Link updated successfully');
    } catch (error) {
      console.error('🚨 Error updating link:', error);
      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    console.log('🔗 LinkService.delete called with id:', id);
    
    try {
      // Get link data first to update user stats
      const link = await this.getById(id);
      
      await deleteDoc(doc(db, COLLECTIONS.LINKS, id));
      
      // Update user's total links count
      if (link?.userId) {
        const userRef = doc(db, COLLECTIONS.USERS, link.userId);
        await updateDoc(userRef, {
          totalLinks: increment(-1)
        });
      }
      
      console.log('✅ Link deleted successfully');
    } catch (error) {
      console.error('🚨 Error deleting link:', error);
      throw error;
    }
  },

  async getByUser(userId: string, limitCount: number = 20): Promise<Link[]> {
    console.log('🔗 LinkService.getByUser called:', { userId, limitCount });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      
      console.log('🔗 Found', links.length, 'links for user');
      return links;
    } catch (error) {
      console.error('🚨 Error getting user links:', error);
      return [];
    }
  },

  async getByCategory(categoryId: string, limitCount: number = 20): Promise<Link[]> {
    console.log('🔗 LinkService.getByCategory called:', { categoryId, limitCount });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('categoryId', '==', categoryId),
        where('isApproved', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      
      console.log('🔗 Found', links.length, 'links for category');
      return links;
    } catch (error) {
      console.error('🚨 Error getting category links:', error);
      return [];
    }
  },

  async getAll(limitCount: number = 20, lastDoc?: QueryDocumentSnapshot<DocumentData>): Promise<{
    links: Link[];
    lastDoc: QueryDocumentSnapshot<DocumentData> | null;
    hasMore: boolean;
  }> {
    console.log('🔗 LinkService.getAll called:', { limitCount, hasLastDoc: !!lastDoc });
    
    try {
      let q = query(
        collection(db, COLLECTIONS.LINKS),
        where('isApproved', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1) // Get one extra to check if there are more
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const docs = querySnapshot.docs;
      
      const hasMore = docs.length > limitCount;
      const links = docs.slice(0, limitCount).map(doc => serializeDoc<Link>(doc));
      const newLastDoc = hasMore ? docs[limitCount - 1] : null;

      console.log('🔗 Found', links.length, 'links, hasMore:', hasMore);
      return { links, lastDoc: newLastDoc, hasMore };
    } catch (error) {
      console.error('🚨 Error getting all links:', error);
      return { links: [], lastDoc: null, hasMore: false };
    }
  },

  async getPending(limitCount: number = 20): Promise<Link[]> {
    console.log('🔗 LinkService.getPending called with limit:', limitCount);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('isApproved', '==', false),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      
      console.log('🔗 Found', links.length, 'pending links');
      return links;
    } catch (error) {
      console.error('🚨 Error getting pending links:', error);
      return [];
    }
  },

  async getTotalCount(): Promise<number> {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.LINKS));
      return querySnapshot.size;
    } catch (error) {
      console.error('🚨 Error getting link count:', error);
      return 0;
    }
  }
};
