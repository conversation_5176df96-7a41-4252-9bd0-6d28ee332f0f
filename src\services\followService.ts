import {
  collection,
  doc,
  getDocs,
  query,
  where,
  writeBatch,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { COLLECTIONS } from '@/firebase/firestore';

export const followService = {
  async follow(followerId: string, followingId: string): Promise<void> {
    console.log('🔄 FollowService.follow called:', { followerId, followingId });
    
    if (followerId === followingId) {
      console.log('⚠️ Cannot follow yourself');
      throw new Error('Cannot follow yourself');
    }
    
    // Check if already following
    const alreadyFollowing = await this.isFollowing(followerId, followingId);
    if (alreadyFollowing) {
      console.log('⚠️ Already following, skipping');
      return;
    }

    try {
      const batch = writeBatch(db);

      // Create follow relationship
      const followRef = doc(collection(db, COLLECTIONS.FOLLOWS));
      batch.set(followRef, {
        followerId,
        followingId,
        createdAt: serverTimestamp()
      });

      // Update user stats
      const followerRef = doc(db, COLLECTIONS.USERS, followerId);
      const followingRef = doc(db, COLLECTIONS.USERS, followingId);
      
      batch.update(followerRef, { totalFollowing: increment(1) });
      batch.update(followingRef, { totalFollowers: increment(1) });

      await batch.commit();
      console.log('✅ Follow relationship created successfully');
    } catch (error) {
      console.error('🚨 Error creating follow relationship:', error);
      throw error;
    }
  },

  async unfollow(followerId: string, followingId: string): Promise<void> {
    console.log('🔄 FollowService.unfollow called:', { followerId, followingId });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', followerId),
        where('followingId', '==', followingId)
      );
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const batch = writeBatch(db);

        // Delete follow relationship
        batch.delete(querySnapshot.docs[0].ref);

        // Update user stats
        const followerRef = doc(db, COLLECTIONS.USERS, followerId);
        const followingRef = doc(db, COLLECTIONS.USERS, followingId);

        batch.update(followerRef, { totalFollowing: increment(-1) });
        batch.update(followingRef, { totalFollowers: increment(-1) });

        await batch.commit();
        console.log('✅ Unfollow relationship deleted successfully');
      } else {
        console.log('⚠️ No follow relationship found to delete');
      }
    } catch (error) {
      console.error('🚨 Error deleting follow relationship:', error);
      throw error;
    }
  },

  async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    console.log('🔍 FollowService.isFollowing called:', { followerId, followingId });
    
    if (followerId === followingId) {
      console.log('🔍 Same user, returning false');
      return false;
    }
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', followerId),
        where('followingId', '==', followingId)
      );
      const querySnapshot = await getDocs(q);
      const isFollowing = !querySnapshot.empty;
      
      console.log('🔍 Follow status result:', isFollowing);
      console.log('🔍 Query returned', querySnapshot.docs.length, 'documents');
      
      return isFollowing;
    } catch (error) {
      console.error('🚨 Error checking follow status:', error);
      return false;
    }
  },

  async getFollowing(userId: string): Promise<string[]> {
    console.log('🔍 FollowService.getFollowing called for userId:', userId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      const followingIds = querySnapshot.docs.map(doc => doc.data().followingId);
      
      console.log('🔍 Following IDs found:', followingIds);
      return followingIds;
    } catch (error) {
      console.error('🚨 Error getting following list:', error);
      return [];
    }
  },

  async getFollowers(userId: string): Promise<string[]> {
    console.log('🔍 FollowService.getFollowers called for userId:', userId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followingId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      const followerIds = querySnapshot.docs.map(doc => doc.data().followerId);
      
      console.log('🔍 Follower IDs found:', followerIds);
      return followerIds;
    } catch (error) {
      console.error('🚨 Error getting followers list:', error);
      return [];
    }
  }
};
