'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { userService, linkService, followService } from '@/firebase/firestore';
import { User, LinkWithDetails } from '@/types';
import LinkCard from '@/components/UI/LinkCard';
import { getInitials } from '@/utils/helpers';
import {
  UserCircleIcon,
  LinkIcon,
  CalendarIcon,
  GlobeAltIcon,
  PencilIcon,
  CheckBadgeIcon,
  UserPlusIcon,
  UserMinusIcon
} from '@heroicons/react/24/outline';

function ProfilePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user: currentUser } = useAuth();
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [userLinks, setUserLinks] = useState<LinkWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [linksLoading, setLinksLoading] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followLoading, setFollowLoading] = useState(false);

  // Get username from URL params or use current user
  const username = searchParams.get('user') || currentUser?.username;
  const isOwnProfile = currentUser?.username === username;

  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!username) {
          if (!currentUser) {
            // Redirect to login if no user specified and not logged in
            router.push('/login');
            return;
          }
          setError('Benutzername nicht gefunden');
          return;
        }

        // Load user data
        const userData = await userService.getByUsername(username);
        if (!userData) {
          setError('Benutzer nicht gefunden');
          return;
        }

        setProfileUser(userData);

        // Check if current user is following this profile (only if not own profile)
        if (currentUser && !isOwnProfile) {
          try {
            const followStatus = await followService.isFollowing(currentUser.id, userData.id);
            setIsFollowing(followStatus);
          } catch (followError) {
            console.error('Error checking follow status:', followError);
          }
        }

        // Load user's links
        setLinksLoading(true);
        try {
          const links = await linkService.getByUserId(userData.id);
          setUserLinks(links);
        } catch (linkError) {
          console.error('Error loading user links:', linkError);
          // Don't set error for links, just show empty state
        } finally {
          setLinksLoading(false);
        }

      } catch (err: any) {
        console.error('Error loading user profile:', err);
        setError('Fehler beim Laden des Profils');
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [username, currentUser, router]);

  const handleFollowToggle = async () => {
    if (!currentUser || !profileUser || isOwnProfile) return;

    setFollowLoading(true);
    try {
      if (isFollowing) {
        await followService.unfollow(currentUser.id, profileUser.id);
        setIsFollowing(false);
        // Update follower count
        setProfileUser(prev => prev ? {
          ...prev,
          totalFollowers: (prev.totalFollowers || 0) - 1
        } : null);
      } else {
        await followService.follow(currentUser.id, profileUser.id);
        setIsFollowing(true);
        // Update follower count
        setProfileUser(prev => prev ? {
          ...prev,
          totalFollowers: (prev.totalFollowers || 0) + 1
        } : null);
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
      // TODO: Show error toast
    } finally {
      setFollowLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Profil wird geladen...</p>
        </div>
      </div>
    );
  }

  if (error || !profileUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <UserCircleIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {error || 'Benutzer nicht gefunden'}
          </h1>
          <p className="text-gray-600 mb-6">
            Der gesuchte Benutzer existiert nicht oder ist nicht verfügbar.
          </p>
          <button
            onClick={() => router.push('/')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            Zur Startseite
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
            <div className="flex items-start space-x-4">
              {/* Avatar */}
              <div className="flex-shrink-0">
                {profileUser.avatar ? (
                  <img
                    src={profileUser.avatar}
                    alt={profileUser.displayName}
                    className="h-20 w-20 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-20 w-20 rounded-full bg-blue-600 flex items-center justify-center text-white text-2xl font-bold">
                    {getInitials(profileUser.displayName)}
                  </div>
                )}
              </div>

              {/* User Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900 truncate">
                    {profileUser.displayName}
                  </h1>
                  {profileUser.isVerified && (
                    <CheckBadgeIcon className="h-6 w-6 text-blue-600" title="Verifizierter Benutzer" />
                  )}
                </div>
                <p className="text-gray-600 mb-2">@{profileUser.username}</p>
                
                {profileUser.bio && (
                  <p className="text-gray-700 mb-4">{profileUser.bio}</p>
                )}

                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Mitglied seit {new Date(profileUser.createdAt).toLocaleDateString('de-DE', {
                      month: 'long',
                      year: 'numeric'
                    })}
                  </div>

                  <div className="flex items-center gap-4">
                    <span>{profileUser.totalFollowers || 0} Follower</span>
                    <span>{profileUser.totalFollowing || 0} Folgt</span>
                  </div>

                  {profileUser.website && (
                    <a
                      href={profileUser.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <GlobeAltIcon className="h-4 w-4 mr-1" />
                      Website
                    </a>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-4 sm:mt-0 flex gap-2">
              {isOwnProfile ? (
                <button
                  onClick={() => router.push('/profile/edit')}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Profil bearbeiten
                </button>
              ) : currentUser && (
                <button
                  onClick={handleFollowToggle}
                  disabled={followLoading}
                  className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    isFollowing
                      ? 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                      : 'border border-transparent text-white bg-blue-600 hover:bg-blue-700'
                  } ${followLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {followLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  ) : isFollowing ? (
                    <UserMinusIcon className="h-4 w-4 mr-2" />
                  ) : (
                    <UserPlusIcon className="h-4 w-4 mr-2" />
                  )}
                  {isFollowing ? 'Entfolgen' : 'Folgen'}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* User Links Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <LinkIcon className="h-5 w-5 text-gray-400" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Geteilte Links
                </h2>
                <span className="bg-gray-100 text-gray-600 text-sm px-2 py-1 rounded-full">
                  {userLinks.length}
                </span>
              </div>
            </div>
          </div>

          <div className="p-6">
            {linksLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Links werden geladen...</p>
              </div>
            ) : userLinks.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {userLinks.map((link) => (
                  <LinkCard key={link.id} link={link} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Noch keine Links geteilt
                </h3>
                <p className="text-gray-600">
                  {isOwnProfile 
                    ? 'Du hast noch keine Links geteilt. Teile deinen ersten Link!'
                    : `${profileUser.displayName} hat noch keine Links geteilt.`
                  }
                </p>
                {isOwnProfile && (
                  <button
                    onClick={() => router.push('/submit')}
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    Ersten Link teilen
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Profil wird geladen...</p>
        </div>
      </div>
    }>
      <ProfilePageContent />
    </Suspense>
  );
}
