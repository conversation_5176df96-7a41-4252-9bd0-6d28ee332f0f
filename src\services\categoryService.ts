import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Category } from '@/types';
import { COLLECTIONS, serializeDoc } from '@/firebase/firestore';

export const categoryService = {
  async create(categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    console.log('📁 CategoryService.create called with data:', categoryData);
    
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.CATEGORIES), {
        ...categoryData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalLinks: 0
      });
      
      console.log('✅ Category created successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('🚨 Error creating category:', error);
      throw error;
    }
  },

  async getById(id: string): Promise<Category | null> {
    console.log('📁 CategoryService.getById called with id:', id);
    
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.CATEGORIES, id));
      if (docSnap.exists()) {
        const categoryData = serializeDoc<Category>(docSnap);
        console.log('📁 Category data loaded:', categoryData);
        return categoryData;
      } else {
        console.log('📁 No category found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in categoryService.getById:', error);
      return null;
    }
  },

  async getByName(name: string): Promise<Category | null> {
    console.log('📁 CategoryService.getByName called with name:', name);
    
    try {
      const q = query(collection(db, COLLECTIONS.CATEGORIES), where('name', '==', name));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        console.log('📁 No category found with name:', name);
        return null;
      }
      
      const categoryData = serializeDoc<Category>(querySnapshot.docs[0]);
      console.log('📁 Category found by name:', categoryData);
      return categoryData;
    } catch (error) {
      console.error('🚨 Error in categoryService.getByName:', error);
      return null;
    }
  },

  async update(id: string, data: Partial<Category>): Promise<void> {
    console.log('📁 CategoryService.update called:', { id, data });
    
    try {
      await updateDoc(doc(db, COLLECTIONS.CATEGORIES, id), {
        ...data,
        updatedAt: serverTimestamp()
      });
      console.log('✅ Category updated successfully');
    } catch (error) {
      console.error('🚨 Error updating category:', error);
      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    console.log('📁 CategoryService.delete called with id:', id);
    
    try {
      await deleteDoc(doc(db, COLLECTIONS.CATEGORIES, id));
      console.log('✅ Category deleted successfully');
    } catch (error) {
      console.error('🚨 Error deleting category:', error);
      throw error;
    }
  },

  async getAll(): Promise<Category[]> {
    console.log('📁 CategoryService.getAll called');
    
    try {
      const q = query(collection(db, COLLECTIONS.CATEGORIES), orderBy('name', 'asc'));
      const querySnapshot = await getDocs(q);
      const categories = querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
      
      console.log('📁 Found', categories.length, 'categories');
      return categories;
    } catch (error) {
      console.error('🚨 Error getting all categories:', error);
      return [];
    }
  },

  async getTopLevel(): Promise<Category[]> {
    console.log('📁 CategoryService.getTopLevel called');
    
    try {
      const q = query(
        collection(db, COLLECTIONS.CATEGORIES),
        where('parentId', '==', null),
        orderBy('name', 'asc')
      );
      const querySnapshot = await getDocs(q);
      const categories = querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
      
      console.log('📁 Found', categories.length, 'top-level categories');
      return categories;
    } catch (error) {
      console.error('🚨 Error getting top-level categories:', error);
      return [];
    }
  },

  async getSubcategories(parentId: string): Promise<Category[]> {
    console.log('📁 CategoryService.getSubcategories called with parentId:', parentId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.CATEGORIES),
        where('parentId', '==', parentId),
        orderBy('name', 'asc')
      );
      const querySnapshot = await getDocs(q);
      const categories = querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
      
      console.log('📁 Found', categories.length, 'subcategories for parent:', parentId);
      return categories;
    } catch (error) {
      console.error('🚨 Error getting subcategories:', error);
      return [];
    }
  },

  async incrementLinkCount(categoryId: string): Promise<void> {
    try {
      const categoryRef = doc(db, COLLECTIONS.CATEGORIES, categoryId);
      await updateDoc(categoryRef, {
        totalLinks: increment(1)
      });
    } catch (error) {
      console.error('🚨 Error incrementing category link count:', error);
    }
  },

  async decrementLinkCount(categoryId: string): Promise<void> {
    try {
      const categoryRef = doc(db, COLLECTIONS.CATEGORIES, categoryId);
      await updateDoc(categoryRef, {
        totalLinks: increment(-1)
      });
    } catch (error) {
      console.error('🚨 Error decrementing category link count:', error);
    }
  },

  async getTotalCount(): Promise<number> {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.CATEGORIES));
      return querySnapshot.size;
    } catch (error) {
      console.error('🚨 Error getting category count:', error);
      return 0;
    }
  }
};
